import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';

import { OrderItem } from './order-item.entity';

@Entity('order_item_options')
@Index(['orderItemId', 'menuItemOptionGroupId', 'menuItemOptionId'], {
  unique: true,
  where: 'deleted_at IS NULL',
})
export class OrderItemOption extends BaseEntity {
  @Column({ name: 'order_item_id', type: 'uuid' })
  orderItemId: string;

  @Column({ name: 'menu_item_option_group_id', type: 'uuid' })
  menuItemOptionGroupId: string;

  @Column({ name: 'menu_item_option_id', type: 'uuid' })
  menuItemOptionId: string;

  @Column({ name: 'amount', default: 1, type: 'integer' })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  price: number;

  @Column({
    name: 'tax_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  taxAmount: number;

  // Duplicate information
  @Column({ name: 'option_group_name', type: 'varchar' })
  optionGroupName: string;

  @Column({ name: 'option_name', type: 'varchar' })
  optionName: string;

  @Column({ name: 'is_alcohol', type: 'boolean', default: false })
  isAlcohol: boolean;

  @ManyToOne(() => OrderItem, (orderItem) => orderItem.orderItemOptions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_item_id' })
  orderItem: WrapperType<OrderItem>;
}
