import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsString, IsUUID, Min } from 'class-validator';

import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

import { MenuItemType } from '../menu-items.constants';

export class ListMenuItemDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by internal name', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Filter by published name', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({ description: 'Filter by minimum base price', required: false })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  minBasePrice?: number;

  @ApiProperty({ description: 'Type of the menu item', enum: MenuItemType })
  @IsOptional()
  @IsEnum(MenuItemType)
  type?: MenuItemType;

  @ApiProperty({ description: 'Filter by maximum base price', required: false })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  maxBasePrice?: number;

  @ApiProperty({ description: 'Filter by restaurant ID', required: false })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;
}
